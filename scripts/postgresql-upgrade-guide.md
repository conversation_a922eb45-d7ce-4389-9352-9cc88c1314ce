# PostgreSQL 15 to 17.5 Upgrade Guide

## Overview
This guide covers upgrading from PostgreSQL 15-alpine to 17.5-alpine in the CrabShield Auth Service.

## Pre-Upgrade Checklist

### 1. Backup Current Database
```bash
# Create a full backup
docker-compose exec auth-postgres pg_dumpall -U auth_user > backup_pre_upgrade_$(date +%Y%m%d_%H%M%S).sql

# Verify backup integrity
docker-compose exec auth-postgres pg_restore --list backup_pre_upgrade_*.sql
```

### 2. Test Environment Setup
```bash
# Create a copy of your docker-compose.yml for testing
cp docker-compose.yml docker-compose.test.yml

# Update test environment to use different ports
sed -i 's/5432:5432/5433:5432/g' docker-compose.test.yml
sed -i 's/8080:8080/8081:8080/g' docker-compose.test.yml
```

## Breaking Changes Addressed

### 1. Search Path Safety (PostgreSQL 17)
- ✅ Updated all custom functions in `docker/postgres-init.sql` to explicitly set `search_path = public`
- Functions affected: `set_tenant_context()`, `get_current_tenant_id()`, `clear_tenant_context()`, `generate_secure_token()`, `update_updated_at_column()`

### 2. JSONB Compatibility
- ✅ Your JSONB usage in migrations is compatible with PostgreSQL 17
- No changes needed for `settings JSONB` columns in tenants and service_clients tables

### 3. Array Types
- ✅ Your `TEXT[]` array usage is fully compatible
- No changes needed for `allowed_origins`, `redirect_uris`, etc.

### 4. Row-Level Security (RLS)
- ✅ Your RLS policies are compatible with PostgreSQL 17
- No changes needed in `migrations/008_create_rls_policies.sql`

## Upgrade Process

### Step 1: Stop Current Services
```bash
docker-compose down
```

### Step 2: Backup Data Volume
```bash
# Create a backup of the data volume
docker run --rm -v crabshield-auth-postgres-data:/data -v $(pwd):/backup alpine tar czf /backup/postgres_data_backup_$(date +%Y%m%d_%H%M%S).tar.gz -C /data .
```

### Step 3: Update Configuration
The following files have been updated:
- ✅ `docker-compose.yml`: Updated to `postgres:17.5-alpine`
- ✅ `docker/postgres-init.sql`: Added search path safety for PostgreSQL 17

### Step 4: Start with New Version
```bash
# Start the upgraded services
docker-compose up -d auth-postgres

# Wait for PostgreSQL to be ready
docker-compose logs -f auth-postgres
```

### Step 5: Verify Database Health
```bash
# Check PostgreSQL version
docker-compose exec auth-postgres psql -U auth_user -d crabshield_auth -c "SELECT version();"

# Verify extensions are loaded
docker-compose exec auth-postgres psql -U auth_user -d crabshield_auth -c "SELECT * FROM pg_extension;"

# Test custom functions
docker-compose exec auth-postgres psql -U auth_user -d crabshield_auth -c "SELECT generate_secure_token(16);"
```

### Step 6: Run Application Tests
```bash
# Start the full stack
docker-compose up -d

# Check application health
curl http://localhost:8080/health

# Run any existing test suites
cargo test
```

## New Features to Explore

### 1. Enhanced VACUUM Performance
PostgreSQL 17 includes improved VACUUM memory management, which will benefit your multi-tenant setup:
- Reduced memory consumption during maintenance
- Better performance under high concurrency

### 2. JSON_TABLE() Function
Consider using the new `JSON_TABLE()` function for processing your JSONB columns:
```sql
-- Example: Extract settings from tenants table
SELECT tenant_id, setting_key, setting_value
FROM tenants,
JSON_TABLE(settings, '$.*' COLUMNS (
    setting_key TEXT PATH '$.key',
    setting_value TEXT PATH '$.value'
));
```

### 3. Improved Query Performance
- Better btree index performance for UUID lookups
- Enhanced sequential read performance
- Improved write throughput under concurrency

## Rollback Plan

If issues arise, you can rollback:

### Step 1: Stop Services
```bash
docker-compose down
```

### Step 2: Restore Data Volume
```bash
# Remove current data volume
docker volume rm crabshield-auth-postgres-data

# Restore from backup
docker run --rm -v crabshield-auth-postgres-data:/data -v $(pwd):/backup alpine tar xzf /backup/postgres_data_backup_*.tar.gz -C /data
```

### Step 3: Revert Configuration
```bash
# Revert docker-compose.yml to postgres:15-alpine
git checkout HEAD -- docker-compose.yml docker/postgres-init.sql
```

### Step 4: Restart with Old Version
```bash
docker-compose up -d
```

## Post-Upgrade Monitoring

### 1. Performance Monitoring
Monitor these metrics after upgrade:
- Query execution times
- VACUUM performance
- Memory usage during maintenance operations
- Connection pool performance

### 2. Application Monitoring
- Authentication flow performance
- Multi-tenant query performance
- JSONB query performance
- Session management performance

## Troubleshooting

### Common Issues

1. **Function Search Path Errors**
   - Symptom: Functions fail with "relation does not exist" errors
   - Solution: Verify all functions have `SET LOCAL search_path = public;`

2. **RLS Policy Issues**
   - Symptom: Tenant isolation not working
   - Solution: Check `current_setting('app.current_tenant_id')` is properly set

3. **JSONB Query Performance**
   - Symptom: Slower JSONB queries
   - Solution: Consider adding GIN indexes on JSONB columns

### Support Resources
- PostgreSQL 17 Release Notes: https://www.postgresql.org/docs/17/release-17.html
- SQLx PostgreSQL 17 Compatibility: https://github.com/launchbadge/sqlx
- Docker PostgreSQL Images: https://hub.docker.com/_/postgres
