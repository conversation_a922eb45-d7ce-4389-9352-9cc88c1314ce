#!/bin/bash

# PostgreSQL Rollback Script for CrabShield Auth Service
# This script rolls back from PostgreSQL 17.5 to PostgreSQL 15

set -euo pipefail

# Configuration
BACKUP_DIR="./backups"
COMPOSE_FILE="docker-compose.yml"
POSTGRES_SERVICE="auth-postgres"
DATABASE_NAME="crabshield_auth"
DATABASE_USER="auth_user"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if backup exists
check_backup() {
    log_info "Checking for backup files..."
    
    if [[ ! -d "$BACKUP_DIR" ]]; then
        log_error "Backup directory not found: $BACKUP_DIR"
        exit 1
    fi
    
    if [[ ! -f "$BACKUP_DIR/latest_backup.env" ]]; then
        log_error "No backup information found. Cannot proceed with rollback."
        exit 1
    fi
    
    # Load backup information
    source "$BACKUP_DIR/latest_backup.env"
    
    if [[ ! -f "$SQL_BACKUP" ]]; then
        log_error "SQL backup file not found: $SQL_BACKUP"
        exit 1
    fi
    
    if [[ ! -f "$VOLUME_BACKUP" ]]; then
        log_error "Volume backup file not found: $VOLUME_BACKUP"
        exit 1
    fi
    
    log_success "Backup files found:"
    log_info "  SQL Backup: $SQL_BACKUP"
    log_info "  Volume Backup: $VOLUME_BACKUP"
    log_info "  Backup Timestamp: $BACKUP_TIMESTAMP"
}

# Confirm rollback
confirm_rollback() {
    log_warning "This will rollback PostgreSQL from 17.5 to 15"
    log_warning "All data changes since the backup will be LOST"
    echo
    read -p "Are you sure you want to proceed? (type 'yes' to confirm): " -r
    echo
    
    if [[ "$REPLY" != "yes" ]]; then
        log_info "Rollback cancelled by user"
        exit 0
    fi
}

# Stop services
stop_services() {
    log_info "Stopping all services..."
    docker-compose down
    log_success "Services stopped"
}

# Restore data volume
restore_data_volume() {
    log_info "Restoring data volume from backup..."
    
    # Remove current data volume
    log_info "Removing current data volume..."
    if docker volume ls | grep -q "crabshield-auth-postgres-data"; then
        docker volume rm crabshield-auth-postgres-data || {
            log_error "Failed to remove data volume. It may be in use."
            log_info "Trying to force remove..."
            docker volume rm -f crabshield-auth-postgres-data || {
                log_error "Failed to force remove data volume"
                exit 1
            }
        }
    fi
    
    # Create new volume and restore data
    log_info "Restoring data from backup..."
    docker run --rm \
        -v crabshield-auth-postgres-data:/data \
        -v "$(pwd)/$BACKUP_DIR":/backup \
        alpine tar xzf "/backup/postgres_data_${BACKUP_TIMESTAMP}.tar.gz" -C /data
    
    log_success "Data volume restored successfully"
}

# Revert configuration files
revert_configuration() {
    log_info "Reverting configuration files..."
    
    # Create backup of current config
    cp "$COMPOSE_FILE" "${COMPOSE_FILE}.pg17.backup"
    
    # Revert docker-compose.yml to PostgreSQL 15
    sed -i.bak 's/postgres:17\.5-alpine/postgres:15-alpine/g' "$COMPOSE_FILE"
    
    # Revert postgres-init.sql if it was modified
    if [[ -f "docker/postgres-init.sql.pg17.backup" ]]; then
        cp "docker/postgres-init.sql.pg17.backup" "docker/postgres-init.sql"
    else
        # Remove PostgreSQL 17 specific search path settings
        sed -i.bak '/-- Explicitly set search_path for security in PostgreSQL 17/d' docker/postgres-init.sql
        sed -i.bak '/SET LOCAL search_path = public;/d' docker/postgres-init.sql
        sed -i.bak '/-- Updated for PostgreSQL 17 search path safety/d' docker/postgres-init.sql
    fi
    
    log_success "Configuration files reverted"
}

# Start PostgreSQL 15
start_postgresql() {
    log_info "Starting PostgreSQL 15..."
    
    docker-compose up -d "$POSTGRES_SERVICE"
    
    # Wait for PostgreSQL to be ready
    log_info "Waiting for PostgreSQL to be ready..."
    local max_attempts=30
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        if docker-compose exec -T "$POSTGRES_SERVICE" pg_isready -U "$DATABASE_USER" -d "$DATABASE_NAME" &> /dev/null; then
            log_success "PostgreSQL is ready"
            break
        fi
        
        log_info "Attempt $attempt/$max_attempts - PostgreSQL not ready yet..."
        sleep 5
        ((attempt++))
    done
    
    if [[ $attempt -gt $max_attempts ]]; then
        log_error "PostgreSQL failed to start within expected time"
        exit 1
    fi
}

# Verify rollback
verify_rollback() {
    log_info "Verifying rollback..."
    
    # Check PostgreSQL version
    local version
    version=$(docker-compose exec -T "$POSTGRES_SERVICE" psql -U "$DATABASE_USER" -d "$DATABASE_NAME" -t -c "SELECT version();" | head -1 | xargs)
    
    log_info "Current version: $version"
    
    if [[ "$version" == *"PostgreSQL 15"* ]]; then
        log_success "Successfully rolled back to PostgreSQL 15"
    else
        log_error "Rollback verification failed. Version: $version"
        return 1
    fi
    
    # Check database connectivity
    if docker-compose exec -T "$POSTGRES_SERVICE" psql -U "$DATABASE_USER" -d "$DATABASE_NAME" -c "SELECT 1;" &> /dev/null; then
        log_success "Database connectivity verified"
    else
        log_error "Database connectivity test failed"
        return 1
    fi
    
    # Check if tables exist
    local table_count
    table_count=$(docker-compose exec -T "$POSTGRES_SERVICE" psql -U "$DATABASE_USER" -d "$DATABASE_NAME" -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';" | xargs)
    
    if [[ "$table_count" -gt 0 ]]; then
        log_success "Database tables are present ($table_count tables found)"
    else
        log_error "No tables found in database"
        return 1
    fi
    
    log_success "Rollback verification completed successfully"
}

# Start application services
start_application() {
    log_info "Starting application services..."
    
    docker-compose up -d
    
    # Wait for application to be ready
    log_info "Waiting for application to be ready..."
    local max_attempts=20
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        if curl -f http://localhost:8080/health &> /dev/null; then
            log_success "Application is ready"
            break
        fi
        
        log_info "Attempt $attempt/$max_attempts - Application not ready yet..."
        sleep 5
        ((attempt++))
    done
    
    if [[ $attempt -gt $max_attempts ]]; then
        log_warning "Application health check timeout - please verify manually"
    fi
}

# Main execution
main() {
    log_info "Starting PostgreSQL rollback process"
    
    check_backup
    confirm_rollback
    stop_services
    restore_data_volume
    revert_configuration
    start_postgresql
    
    if verify_rollback; then
        start_application
        log_success "PostgreSQL rollback completed successfully!"
        log_info "Application should be accessible at: http://localhost:8080"
        log_info "PostgreSQL 17 configuration backup saved as: ${COMPOSE_FILE}.pg17.backup"
    else
        log_error "Rollback verification failed"
        exit 1
    fi
}

# Handle script interruption
trap 'log_error "Script interrupted"; exit 1' INT TERM

# Run main function
main "$@"
