#!/bin/bash

# PostgreSQL Upgrade Test Script
# This script runs comprehensive tests after the PostgreSQL upgrade

set -euo pipefail

# Configuration
POSTGRES_SERVICE="auth-postgres"
DATABASE_NAME="crabshield_auth"
DATABASE_USER="auth_user"
APP_URL="http://localhost:8080"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test counters
TESTS_PASSED=0
TESTS_FAILED=0

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
    ((TESTS_PASSED++))
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
    ((TESTS_FAILED++))
}

log_test() {
    echo -e "${BLUE}[TEST]${NC} $1"
}

# Test PostgreSQL version
test_postgresql_version() {
    log_test "Testing PostgreSQL version..."
    
    local version
    version=$(docker-compose exec -T "$POSTGRES_SERVICE" psql -U "$DATABASE_USER" -d "$DATABASE_NAME" -t -c "SELECT version();" | head -1 | xargs)
    
    if [[ "$version" == *"PostgreSQL 17"* ]]; then
        log_success "PostgreSQL 17 is running: $version"
    else
        log_error "Expected PostgreSQL 17, got: $version"
    fi
}

# Test database connectivity
test_database_connectivity() {
    log_test "Testing database connectivity..."
    
    if docker-compose exec -T "$POSTGRES_SERVICE" psql -U "$DATABASE_USER" -d "$DATABASE_NAME" -c "SELECT 1;" &> /dev/null; then
        log_success "Database connectivity test passed"
    else
        log_error "Database connectivity test failed"
    fi
}

# Test extensions
test_extensions() {
    log_test "Testing PostgreSQL extensions..."
    
    local extensions
    extensions=$(docker-compose exec -T "$POSTGRES_SERVICE" psql -U "$DATABASE_USER" -d "$DATABASE_NAME" -t -c "SELECT extname FROM pg_extension ORDER BY extname;")
    
    if echo "$extensions" | grep -q "uuid-ossp"; then
        log_success "uuid-ossp extension is available"
    else
        log_error "uuid-ossp extension is missing"
    fi
    
    if echo "$extensions" | grep -q "pgcrypto"; then
        log_success "pgcrypto extension is available"
    else
        log_error "pgcrypto extension is missing"
    fi
}

# Test custom functions
test_custom_functions() {
    log_test "Testing custom functions..."
    
    # Test generate_secure_token function
    local token
    token=$(docker-compose exec -T "$POSTGRES_SERVICE" psql -U "$DATABASE_USER" -d "$DATABASE_NAME" -t -c "SELECT generate_secure_token(16);" | xargs)
    
    if [[ ${#token} -eq 32 ]]; then  # 16 bytes = 32 hex chars
        log_success "generate_secure_token function works correctly"
    else
        log_error "generate_secure_token function failed. Expected 32 chars, got ${#token}"
    fi
    
    # Test tenant context functions
    if docker-compose exec -T "$POSTGRES_SERVICE" psql -U "$DATABASE_USER" -d "$DATABASE_NAME" -c "SELECT set_tenant_context('00000000-0000-0000-0000-000000000000'::UUID);" &> /dev/null; then
        log_success "set_tenant_context function works correctly"
    else
        log_error "set_tenant_context function failed"
    fi
    
    if docker-compose exec -T "$POSTGRES_SERVICE" psql -U "$DATABASE_USER" -d "$DATABASE_NAME" -c "SELECT get_current_tenant_id();" &> /dev/null; then
        log_success "get_current_tenant_id function works correctly"
    else
        log_error "get_current_tenant_id function failed"
    fi
    
    if docker-compose exec -T "$POSTGRES_SERVICE" psql -U "$DATABASE_USER" -d "$DATABASE_NAME" -c "SELECT clear_tenant_context();" &> /dev/null; then
        log_success "clear_tenant_context function works correctly"
    else
        log_error "clear_tenant_context function failed"
    fi
}

# Test table structure
test_table_structure() {
    log_test "Testing table structure..."
    
    # Check if main tables exist
    local tables=("users" "user_sessions" "tenants" "service_clients" "user_mfa_secrets")
    
    for table in "${tables[@]}"; do
        local exists
        exists=$(docker-compose exec -T "$POSTGRES_SERVICE" psql -U "$DATABASE_USER" -d "$DATABASE_NAME" -t -c "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = '$table');" | xargs)
        
        if [[ "$exists" == "t" ]]; then
            log_success "Table '$table' exists"
        else
            log_error "Table '$table' is missing"
        fi
    done
}

# Test JSONB functionality
test_jsonb_functionality() {
    log_test "Testing JSONB functionality..."
    
    # Test JSONB operations on tenants table
    local jsonb_test
    jsonb_test=$(docker-compose exec -T "$POSTGRES_SERVICE" psql -U "$DATABASE_USER" -d "$DATABASE_NAME" -t -c "SELECT '{}' :: jsonb;" | xargs)
    
    if [[ "$jsonb_test" == "{}" ]]; then
        log_success "JSONB functionality works correctly"
    else
        log_error "JSONB functionality test failed"
    fi
    
    # Test JSONB operations with existing data
    local tenant_count
    tenant_count=$(docker-compose exec -T "$POSTGRES_SERVICE" psql -U "$DATABASE_USER" -d "$DATABASE_NAME" -t -c "SELECT COUNT(*) FROM tenants WHERE settings IS NOT NULL;" | xargs)
    
    if [[ "$tenant_count" -ge 0 ]]; then
        log_success "JSONB columns in tenants table are accessible"
    else
        log_error "JSONB columns in tenants table are not accessible"
    fi
}

# Test array functionality
test_array_functionality() {
    log_test "Testing array functionality..."
    
    # Test array operations
    local array_test
    array_test=$(docker-compose exec -T "$POSTGRES_SERVICE" psql -U "$DATABASE_USER" -d "$DATABASE_NAME" -t -c "SELECT ARRAY['test1', 'test2']::TEXT[];" | xargs)
    
    if [[ "$array_test" == *"test1"* && "$array_test" == *"test2"* ]]; then
        log_success "Array functionality works correctly"
    else
        log_error "Array functionality test failed"
    fi
}

# Test RLS policies
test_rls_policies() {
    log_test "Testing Row-Level Security policies..."
    
    # Check if RLS is enabled on users table
    local rls_enabled
    rls_enabled=$(docker-compose exec -T "$POSTGRES_SERVICE" psql -U "$DATABASE_USER" -d "$DATABASE_NAME" -t -c "SELECT relrowsecurity FROM pg_class WHERE relname = 'users';" | xargs)
    
    if [[ "$rls_enabled" == "t" ]]; then
        log_success "RLS is enabled on users table"
    else
        log_error "RLS is not enabled on users table"
    fi
    
    # Check if policies exist
    local policy_count
    policy_count=$(docker-compose exec -T "$POSTGRES_SERVICE" psql -U "$DATABASE_USER" -d "$DATABASE_NAME" -t -c "SELECT COUNT(*) FROM pg_policies WHERE tablename = 'users';" | xargs)
    
    if [[ "$policy_count" -gt 0 ]]; then
        log_success "RLS policies exist for users table ($policy_count policies)"
    else
        log_error "No RLS policies found for users table"
    fi
}

# Test application health
test_application_health() {
    log_test "Testing application health..."
    
    # Check if application is responding
    if curl -f "$APP_URL/health" &> /dev/null; then
        log_success "Application health endpoint is responding"
    else
        log_error "Application health endpoint is not responding"
    fi
    
    # Check application logs for errors
    local error_count
    error_count=$(docker-compose logs auth-service 2>&1 | grep -i error | wc -l)
    
    if [[ "$error_count" -eq 0 ]]; then
        log_success "No errors found in application logs"
    else
        log_warning "Found $error_count error messages in application logs"
    fi
}

# Test new PostgreSQL 17 features
test_postgresql17_features() {
    log_test "Testing PostgreSQL 17 specific features..."
    
    # Test JSON_TABLE function (new in PostgreSQL 17)
    local json_table_test
    json_table_test=$(docker-compose exec -T "$POSTGRES_SERVICE" psql -U "$DATABASE_USER" -d "$DATABASE_NAME" -t -c "SELECT COUNT(*) FROM JSON_TABLE('{\"key\": \"value\"}', '$' COLUMNS (key TEXT PATH '$.key')) AS jt;" 2>/dev/null | xargs || echo "0")
    
    if [[ "$json_table_test" == "1" ]]; then
        log_success "JSON_TABLE function is available and working"
    else
        log_warning "JSON_TABLE function test failed (this is expected if not using this feature)"
    fi
    
    # Test improved VACUUM (check if it runs without errors)
    if docker-compose exec -T "$POSTGRES_SERVICE" psql -U "$DATABASE_USER" -d "$DATABASE_NAME" -c "VACUUM ANALYZE;" &> /dev/null; then
        log_success "VACUUM ANALYZE runs successfully with PostgreSQL 17 improvements"
    else
        log_error "VACUUM ANALYZE failed"
    fi
}

# Performance baseline test
test_performance_baseline() {
    log_test "Running performance baseline test..."
    
    # Simple query performance test
    local start_time=$(date +%s%N)
    docker-compose exec -T "$POSTGRES_SERVICE" psql -U "$DATABASE_USER" -d "$DATABASE_NAME" -c "SELECT COUNT(*) FROM users;" &> /dev/null
    local end_time=$(date +%s%N)
    local duration=$(( (end_time - start_time) / 1000000 )) # Convert to milliseconds
    
    log_info "Simple COUNT query took ${duration}ms"
    
    if [[ "$duration" -lt 1000 ]]; then  # Less than 1 second
        log_success "Query performance is acceptable"
    else
        log_warning "Query performance may need optimization"
    fi
}

# Main test execution
main() {
    log_info "Starting PostgreSQL upgrade test suite..."
    echo
    
    # Run all tests
    test_postgresql_version
    test_database_connectivity
    test_extensions
    test_custom_functions
    test_table_structure
    test_jsonb_functionality
    test_array_functionality
    test_rls_policies
    test_application_health
    test_postgresql17_features
    test_performance_baseline
    
    # Summary
    echo
    log_info "Test Summary:"
    log_info "  Tests Passed: $TESTS_PASSED"
    log_info "  Tests Failed: $TESTS_FAILED"
    
    if [[ "$TESTS_FAILED" -eq 0 ]]; then
        log_success "All tests passed! PostgreSQL upgrade is successful."
        exit 0
    else
        log_error "Some tests failed. Please review the issues above."
        exit 1
    fi
}

# Handle script interruption
trap 'log_error "Test interrupted"; exit 1' INT TERM

# Run main function
main "$@"
