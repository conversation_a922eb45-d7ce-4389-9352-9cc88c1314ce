#!/bin/bash

# PostgreSQL 15 to 17.5 Upgrade Script for CrabShield Auth Service
# This script automates the upgrade process with safety checks and rollback capability

set -euo pipefail

# Configuration
BACKUP_DIR="./backups"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
COMPOSE_FILE="docker-compose.yml"
POSTGRES_SERVICE="auth-postgres"
DATABASE_NAME="crabshield_auth"
DATABASE_USER="auth_user"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if docker-compose is available
    if ! command -v docker-compose &> /dev/null; then
        log_error "docker-compose is not installed or not in PATH"
        exit 1
    fi
    
    # Check if docker is running
    if ! docker info &> /dev/null; then
        log_error "Docker is not running"
        exit 1
    fi
    
    # Check if compose file exists
    if [[ ! -f "$COMPOSE_FILE" ]]; then
        log_error "docker-compose.yml not found"
        exit 1
    fi
    
    # Create backup directory
    mkdir -p "$BACKUP_DIR"
    
    log_success "Prerequisites check passed"
}

# Create database backup
create_backup() {
    log_info "Creating database backup..."
    
    # Check if PostgreSQL service is running
    if ! docker-compose ps "$POSTGRES_SERVICE" | grep -q "Up"; then
        log_warning "PostgreSQL service is not running. Starting it..."
        docker-compose up -d "$POSTGRES_SERVICE"
        sleep 10
    fi
    
    # Create SQL dump
    local backup_file="$BACKUP_DIR/postgres_dump_${TIMESTAMP}.sql"
    log_info "Creating SQL dump: $backup_file"
    
    if docker-compose exec -T "$POSTGRES_SERVICE" pg_dumpall -U "$DATABASE_USER" > "$backup_file"; then
        log_success "SQL dump created successfully"
    else
        log_error "Failed to create SQL dump"
        exit 1
    fi
    
    # Create data volume backup
    local volume_backup="$BACKUP_DIR/postgres_data_${TIMESTAMP}.tar.gz"
    log_info "Creating data volume backup: $volume_backup"
    
    if docker run --rm \
        -v crabshield-auth-postgres-data:/data \
        -v "$(pwd)/$BACKUP_DIR":/backup \
        alpine tar czf "/backup/postgres_data_${TIMESTAMP}.tar.gz" -C /data .; then
        log_success "Data volume backup created successfully"
    else
        log_error "Failed to create data volume backup"
        exit 1
    fi
    
    # Store backup info
    echo "BACKUP_TIMESTAMP=$TIMESTAMP" > "$BACKUP_DIR/latest_backup.env"
    echo "SQL_BACKUP=$backup_file" >> "$BACKUP_DIR/latest_backup.env"
    echo "VOLUME_BACKUP=$volume_backup" >> "$BACKUP_DIR/latest_backup.env"
    
    log_success "Backup completed successfully"
}

# Verify current PostgreSQL version
verify_current_version() {
    log_info "Verifying current PostgreSQL version..."
    
    local current_version
    current_version=$(docker-compose exec -T "$POSTGRES_SERVICE" psql -U "$DATABASE_USER" -d "$DATABASE_NAME" -t -c "SELECT version();" | head -1 | xargs)
    
    log_info "Current version: $current_version"
    
    if [[ "$current_version" == *"PostgreSQL 15"* ]]; then
        log_success "Confirmed PostgreSQL 15 installation"
        return 0
    else
        log_warning "Current version is not PostgreSQL 15: $current_version"
        read -p "Continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "Upgrade cancelled by user"
            exit 0
        fi
    fi
}

# Perform the upgrade
perform_upgrade() {
    log_info "Starting PostgreSQL upgrade process..."
    
    # Stop all services
    log_info "Stopping all services..."
    docker-compose down
    
    # Verify the docker-compose.yml has been updated
    if ! grep -q "postgres:17.5-alpine" "$COMPOSE_FILE"; then
        log_error "docker-compose.yml has not been updated to postgres:17.5-alpine"
        log_error "Please ensure the image has been updated before running this script"
        exit 1
    fi
    
    # Start PostgreSQL with new version
    log_info "Starting PostgreSQL 17.5..."
    docker-compose up -d "$POSTGRES_SERVICE"
    
    # Wait for PostgreSQL to be ready
    log_info "Waiting for PostgreSQL to be ready..."
    local max_attempts=30
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        if docker-compose exec -T "$POSTGRES_SERVICE" pg_isready -U "$DATABASE_USER" -d "$DATABASE_NAME" &> /dev/null; then
            log_success "PostgreSQL is ready"
            break
        fi
        
        log_info "Attempt $attempt/$max_attempts - PostgreSQL not ready yet..."
        sleep 5
        ((attempt++))
    done
    
    if [[ $attempt -gt $max_attempts ]]; then
        log_error "PostgreSQL failed to start within expected time"
        exit 1
    fi
}

# Verify upgrade
verify_upgrade() {
    log_info "Verifying upgrade..."
    
    # Check PostgreSQL version
    local new_version
    new_version=$(docker-compose exec -T "$POSTGRES_SERVICE" psql -U "$DATABASE_USER" -d "$DATABASE_NAME" -t -c "SELECT version();" | head -1 | xargs)
    
    log_info "New version: $new_version"
    
    if [[ "$new_version" == *"PostgreSQL 17"* ]]; then
        log_success "Successfully upgraded to PostgreSQL 17"
    else
        log_error "Upgrade verification failed. Version: $new_version"
        return 1
    fi
    
    # Check extensions
    log_info "Verifying extensions..."
    local extensions
    extensions=$(docker-compose exec -T "$POSTGRES_SERVICE" psql -U "$DATABASE_USER" -d "$DATABASE_NAME" -t -c "SELECT extname FROM pg_extension ORDER BY extname;")
    
    if echo "$extensions" | grep -q "uuid-ossp" && echo "$extensions" | grep -q "pgcrypto"; then
        log_success "Required extensions are available"
    else
        log_error "Required extensions are missing"
        return 1
    fi
    
    # Test custom functions
    log_info "Testing custom functions..."
    if docker-compose exec -T "$POSTGRES_SERVICE" psql -U "$DATABASE_USER" -d "$DATABASE_NAME" -t -c "SELECT generate_secure_token(16);" &> /dev/null; then
        log_success "Custom functions are working"
    else
        log_error "Custom functions test failed"
        return 1
    fi
    
    log_success "Upgrade verification completed successfully"
}

# Start application services
start_application() {
    log_info "Starting application services..."
    
    docker-compose up -d
    
    # Wait for application to be ready
    log_info "Waiting for application to be ready..."
    local max_attempts=20
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        if curl -f http://localhost:8080/health &> /dev/null; then
            log_success "Application is ready"
            break
        fi
        
        log_info "Attempt $attempt/$max_attempts - Application not ready yet..."
        sleep 5
        ((attempt++))
    done
    
    if [[ $attempt -gt $max_attempts ]]; then
        log_warning "Application health check timeout - please verify manually"
    fi
}

# Main execution
main() {
    log_info "Starting PostgreSQL 15 to 17.5 upgrade process"
    
    check_prerequisites
    verify_current_version
    create_backup
    perform_upgrade
    
    if verify_upgrade; then
        start_application
        log_success "PostgreSQL upgrade completed successfully!"
        log_info "Backup files are available in: $BACKUP_DIR"
        log_info "Application should be accessible at: http://localhost:8080"
    else
        log_error "Upgrade verification failed"
        log_warning "Consider rolling back using: ./scripts/rollback-postgresql.sh"
        exit 1
    fi
}

# Handle script interruption
trap 'log_error "Script interrupted"; exit 1' INT TERM

# Run main function
main "$@"
